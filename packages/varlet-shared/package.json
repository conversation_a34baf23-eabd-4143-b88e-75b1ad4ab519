{"name": "@varlet/shared", "version": "3.11.1", "description": "shared utils of varlet", "keywords": ["shared", "utils", "varlet"], "bugs": {"url": "https://github.com/varletjs/varlet/issues"}, "repository": {"type": "git", "url": "https://github.com/varletjs/varlet.git"}, "license": "MIT", "author": "haoziqaq <<EMAIL>>", "sideEffects": false, "type": "module", "exports": {".": {"import": "./lib/index.js", "require": "./lib/index.cjs", "types": "./lib/index.d.ts"}}, "main": "lib/index.cjs", "module": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "scripts": {"build": "tsup src/index.ts --format esm,cjs --out-dir=lib --dts --clean", "dev": "tsup src/index.ts --format esm --out-dir=lib --watch --dts"}, "dependencies": {"rattail": "1.0.17"}, "devDependencies": {"@types/node": "catalog:", "tsup": "catalog:", "typescript": "catalog:"}}