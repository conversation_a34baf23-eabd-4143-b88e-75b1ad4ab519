import { type ButtonHTMLAttributes, type PropType } from 'vue'
import { loadingProps } from '../loading'
import { defineListenerProp, pickProps } from '../utils/components'

export type ButtonType = 'default' | 'primary' | 'info' | 'success' | 'warning' | 'danger'

export type ButtonSize = 'normal' | 'mini' | 'small' | 'large'

export const props = {
  type: String as PropType<ButtonType>,
  nativeType: {
    type: String as PropType<ButtonHTMLAttributes['type']>,
    default: 'button',
  },
  size: String as PropType<ButtonSize>,
  loading: Boolean,
  round: Boolean,
  block: <PERSON>olean,
  text: <PERSON>olean,
  outline: Boolean,
  disabled: Boolean,
  autoLoading: <PERSON>olean,
  iconContainer: Boolean,
  ripple: {
    type: Boolean,
    default: true,
  },
  focusable: {
    type: Boolean,
    default: true,
  },
  color: String,
  textColor: String,
  elevation: {
    type: [Boolean, Number, String],
    default: true,
  },
  loadingRadius: [Number, String],
  loadingType: pickProps(loadingProps, 'type'),
  loadingSize: {
    ...pickProps(loadingProps, 'size'),
    default: undefined,
  },
  loadingColor: {
    ...pickProps(loadingProps, 'color'),
    default: 'currentColor',
  },
  onClick: defineListenerProp<(e: Event) => void | Promise<any>>(),
  onTouchstart: defineListenerProp<(e: Event) => void | Promise<any>>(),
}
