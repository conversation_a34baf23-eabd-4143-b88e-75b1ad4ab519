:root {
  --button-default-text-color: #555;
  --button-primary-text-color: var(--color-on-primary);
  --button-danger-text-color: var(--color-on-danger);
  --button-success-text-color: var(--color-on-success);
  --button-warning-text-color: var(--color-on-warning);
  --button-info-text-color: var(--color-on-info);
  --button-default-color: #f5f5f5;
  --button-primary-color: var(--color-primary);
  --button-danger-color: var(--color-danger);
  --button-success-color: var(--color-success);
  --button-warning-color: var(--color-warning);
  --button-info-color: var(--color-info);
  --button-default-icon-color: #555;
  --button-primary-icon-color: var(--color-on-primary-container);
  --button-danger-icon-color: var(--color-on-danger-container);
  --button-success-icon-color: var(--color-on-success-container);
  --button-warning-icon-color: var(--color-on-warning-container);
  --button-info-icon-color: var(--color-on-info-container);
  --button-default-icon-container-color: #f5f5f5;
  --button-primary-icon-container-color: var(--color-primary-container);
  --button-danger-icon-container-color: var(--color-danger-container);
  --button-success-icon-container-color: var(--color-success-container);
  --button-warning-icon-container-color: var(--color-warning-container);
  --button-info-icon-container-color: var(--color-info-container);
  --button-disabled-color: var(--color-disabled);
  --button-disabled-text-color: var(--color-text-disabled);
  --button-border-radius: 4px;
  --button-mini-padding: 0 8px;
  --button-small-padding: 0 12px;
  --button-normal-padding: 0 16px;
  --button-large-padding: 0 22px;
  --button-round-padding: 6px;
  --button-mini-height: 20px;
  --button-small-height: 28px;
  --button-normal-height: 36px;
  --button-large-height: 44px;
  --button-mini-font-size: var(--font-size-xs);
  --button-small-font-size: var(--font-size-sm);
  --button-normal-font-size: var(--font-size-md);
  --button-large-font-size: var(--font-size-lg);
}

.var-button {
  position: relative;
  justify-content: center;
  align-items: center;
  outline: none;
  border: none;
  border-radius: var(--button-border-radius);
  user-select: none;
  cursor: pointer;
  font-family: inherit;
  transition:
    box-shadow 0.2s,
    background-color 0.25s,
    border-radius 0.2s;
  will-change: box-shadow;
  -webkit-tap-highlight-color: transparent;
  white-space: nowrap;

  &:active {
    box-shadow:
      0 3px 5px -1px var(--shadow-key-umbra-opacity),
      0 5px 8px 0 var(--shadow-key-penumbra-opacity),
      0 1px 14px 0 var(--shadow-key-ambient-opacity);
  }

  &__content {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }

  &__loading[var-button-cover] {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  &--default {
    color: var(--button-default-text-color);
    background-color: var(--button-default-color);
  }

  &--primary {
    color: var(--button-primary-text-color);
    background-color: var(--button-primary-color);
  }

  &--info {
    color: var(--button-info-text-color);
    background-color: var(--button-info-color);
  }

  &--success {
    color: var(--button-success-text-color);
    background-color: var(--button-success-color);
  }

  &--warning {
    color: var(--button-warning-text-color);
    background-color: var(--button-warning-color);
  }

  &--danger {
    color: var(--button-danger-text-color);
    background-color: var(--button-danger-color);
  }

  &--loading {
    cursor: default;
  }

  &--block {
    width: 100%;
  }

  &--text-default {
    color: inherit;
  }

  &--text-primary {
    color: var(--button-primary-color);
  }

  &--text-info {
    color: var(--button-info-color);
  }

  &--text-success {
    color: var(--button-success-color);
  }

  &--text-warning {
    color: var(--button-warning-color);
  }

  &--text-danger {
    color: var(--button-danger-color);
  }

  &--icon-container-default {
    color: var(--button-default-icon-color);
    background-color: var(--button-default-icon-container-color);
  }

  &--icon-container-primary {
    color: var(--button-primary-icon-color);
    background-color: var(--button-primary-icon-container-color);
  }

  &--icon-container-info {
    color: var(--button-info-icon-color);
    background-color: var(--button-info-icon-container-color);
  }

  &--icon-container-success {
    color: var(--button-success-icon-color);
    background-color: var(--button-success-icon-container-color);
  }

  &--icon-container-warning {
    color: var(--button-warning-icon-color);
    background-color: var(--button-warning-icon-container-color);
  }

  &--icon-container-danger {
    color: var(--button-danger-icon-color);
    background-color: var(--button-danger-icon-container-color);
  }

  &--normal {
    height: var(--button-normal-height);
    padding: var(--button-normal-padding);
    font-size: var(--button-normal-font-size);
  }

  &--large {
    height: var(--button-large-height);
    padding: var(--button-large-padding);
    font-size: var(--button-large-font-size);
  }

  &--small {
    height: var(--button-small-height);
    padding: var(--button-small-padding);
    font-size: var(--button-small-font-size);
  }

  &--mini {
    height: var(--button-mini-height);
    padding: var(--button-mini-padding);
    font-size: var(--button-mini-font-size);
  }

  &--round {
    padding: var(--button-round-padding);
    border-radius: 50%;
    height: auto;
  }

  &--outline {
    border: thin solid currentColor;
  }

  &--hidden {
    opacity: 0;
  }

  &--disabled {
    background-color: var(--button-disabled-color);
    color: var(--button-disabled-text-color);
    cursor: not-allowed;
    box-shadow: none !important;
  }

  &--text-disabled {
    color: var(--button-disabled-text-color);
  }

  &--text {
    background-color: transparent;

    &:active {
      box-shadow: none;
    }
  }
}
