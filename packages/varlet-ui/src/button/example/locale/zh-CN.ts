export default {
  themeColorButton: '主题色按钮',
  themeColorButtonGroup: '按钮组主题色',
  defaultButton: '默认按钮',
  primaryButton: '主要按钮',
  infoButton: '信息按钮',
  successButton: '成功按钮',
  warningButton: '警告按钮',
  dangerButton: '危险按钮',
  textButton: '文字按钮',
  modeButtonGroup: '按钮组模式',
  plainTextButton: '纯文字按钮',
  outlineButton: '外边框按钮',
  outline: '边框',
  disabledStatus: '禁用状态',
  loadingStatus: '加载状态',
  buttonSize: '按钮尺寸',
  buttonGroupSize: '按钮组尺寸',
  normalButton: '常规按钮',
  smallButton: '小型按钮',
  miniButton: '迷你按钮',
  largeButton: '大型按钮',
  blockButton: '块级按钮',
  customColor: '自定义颜色',
  customButtonGroupColor: '自定义按钮组颜色',
  backgroundTextColor: '背景/文字',
  linearGradientColor: '使用渐变',
  verticalButtonGroup: '按钮组竖直排列',
  roundButton: '圆形按钮',
  event: '注册事件',
  click: '点击',
  touchstart: '触摸',
  autoLoading: '自动加载',
  clickSuccess: '点击成功',
  touchstartSuccess: '触摸成功',
  button: '按钮',
  splitButton: '按钮拆分',
}
