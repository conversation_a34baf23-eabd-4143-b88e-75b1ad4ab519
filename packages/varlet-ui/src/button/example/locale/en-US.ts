export default {
  themeColorButton: 'Theme Color Button',
  themeColorButtonGroup: 'Button Group Theme Color',
  defaultButton: 'Default Button',
  default: 'Default',
  primaryButton: 'Primary Button',
  infoButton: 'Info Button',
  successButton: 'Success Button',
  warningButton: 'Warning Button',
  dangerButton: 'Danger Button',
  textButton: 'Text Button',
  modeButtonGroup: 'Button Group Mode',
  plainTextButton: 'Plain Text Button',
  outlineButton: 'Outline Button',
  outline: 'Outline',
  disabledStatus: 'Disabled Status',
  loadingStatus: 'Loading Status',
  buttonSize: 'Button Size',
  buttonGroupSize: 'Button Group Size',
  normalButton: 'Normal Button',
  smallButton: 'Small Button',
  miniButton: 'Mini Button',
  largeButton: 'Large Button',
  blockButton: 'Block Button',
  customColor: 'Custom Color',
  customButtonGroupColor: 'Custom Button Group Color',
  textColor: 'Text Color',
  backgroundTextColor: 'Background/Text Color',
  verticalButtonGroup: 'Vertical Button Group',
  linearGradientColor: 'LinearGradient Color',
  roundButton: 'Round Button',
  event: 'Events',
  click: 'Click',
  touchstart: 'Touchstart',
  autoLoading: 'Auto Loading',
  clickSuccess: 'Click Success',
  touchstartSuccess: 'Touchstart Success',
  button: 'Button',
  splitButton: 'Split Button',
}
