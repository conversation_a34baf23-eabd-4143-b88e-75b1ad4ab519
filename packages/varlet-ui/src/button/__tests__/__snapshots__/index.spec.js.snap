// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`test button component props > button focusable 1`] = `
"<button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--default" tabindex="-1" type="button">
  <!--v-if-->
  <div class="var-button__content"></div>
  <div class="var-hover-overlay"></div>
</button>"
`;

exports[`test button group component props > button group color and text-color 1`] = `
"<div class="var-button-group var--box var-button-group--mode-normal var-button-group--horizontal var-elevation--2"><button class="var-button var--box var-button--normal var--inline-flex var-button--default" style="color: red; background: yellow;" type="button">
    <!--v-if-->
    <div class="var-button__content"></div>
    <div class="var-hover-overlay"></div>
  </button><button class="var-button var--box var-button--normal var--inline-flex var-button--default" style="color: red; background: yellow;" type="button">
    <!--v-if-->
    <div class="var-button__content"></div>
    <div class="var-hover-overlay"></div>
  </button><button class="var-button var--box var-button--normal var--inline-flex var-button--default" style="color: red; background: yellow;" type="button">
    <!--v-if-->
    <div class="var-button__content"></div>
    <div class="var-hover-overlay"></div>
  </button></div>"
`;

exports[`test button group component props > button group elevation 1`] = `
"<div class="var-button-group var--box var-button-group--mode-normal var-button-group--horizontal var-elevation--10"><button class="var-button var--box var-button--normal var--inline-flex var-button--default" type="button">
    <!--v-if-->
    <div class="var-button__content"></div>
    <div class="var-hover-overlay"></div>
  </button><button class="var-button var--box var-button--normal var--inline-flex var-button--default" type="button">
    <!--v-if-->
    <div class="var-button__content"></div>
    <div class="var-hover-overlay"></div>
  </button><button class="var-button var--box var-button--normal var--inline-flex var-button--default" type="button">
    <!--v-if-->
    <div class="var-button__content"></div>
    <div class="var-hover-overlay"></div>
  </button></div>"
`;

exports[`test button group component props > button group icon-container mode 1`] = `
"<div class="var-button-group var--box var-button-group--mode-icon-container var-button-group--horizontal"><button class="var-button var--box var-button--normal var--inline-flex var-button--icon-container-default" type="button">
    <!--v-if-->
    <div class="var-button__content"></div>
    <div class="var-hover-overlay"></div>
  </button><button class="var-button var--box var-button--normal var--inline-flex var-button--icon-container-default" type="button">
    <!--v-if-->
    <div class="var-button__content"></div>
    <div class="var-hover-overlay"></div>
  </button><button class="var-button var--box var-button--normal var--inline-flex var-button--icon-container-default" type="button">
    <!--v-if-->
    <div class="var-button__content"></div>
    <div class="var-hover-overlay"></div>
  </button></div>"
`;

exports[`test button group component props > button group outline mode 1`] = `
"<div class="var-button-group var--box var-button-group--mode-outline var-button-group--horizontal"><button class="var-button var--box var-button--normal var--inline-flex var-button--text var-button--text-default var-button--outline" type="button">
    <!--v-if-->
    <div class="var-button__content"></div>
    <div class="var-hover-overlay"></div>
  </button><button class="var-button var--box var-button--normal var--inline-flex var-button--text var-button--text-default var-button--outline" type="button">
    <!--v-if-->
    <div class="var-button__content"></div>
    <div class="var-hover-overlay"></div>
  </button><button class="var-button var--box var-button--normal var--inline-flex var-button--text var-button--text-default var-button--outline" type="button">
    <!--v-if-->
    <div class="var-button__content"></div>
    <div class="var-hover-overlay"></div>
  </button></div>"
`;

exports[`test button group component props > button group text mode 1`] = `
"<div class="var-button-group var--box var-button-group--mode-text var-button-group--horizontal"><button class="var-button var--box var-button--normal var--inline-flex var-button--text var-button--text-default" type="button">
    <!--v-if-->
    <div class="var-button__content"></div>
    <div class="var-hover-overlay"></div>
  </button><button class="var-button var--box var-button--normal var--inline-flex var-button--text var-button--text-default" type="button">
    <!--v-if-->
    <div class="var-button__content"></div>
    <div class="var-hover-overlay"></div>
  </button><button class="var-button var--box var-button--normal var--inline-flex var-button--text var-button--text-default" type="button">
    <!--v-if-->
    <div class="var-button__content"></div>
    <div class="var-hover-overlay"></div>
  </button></div>"
`;

exports[`test button group component props > button group type and size 1`] = `
"<div class="var-button-group var--box var-button-group--mode-normal var-button-group--horizontal var-elevation--2"><button class="var-button var--box var-button--large var--inline-flex var-button--primary" type="button">
    <!--v-if-->
    <div class="var-button__content"></div>
    <div class="var-hover-overlay"></div>
  </button><button class="var-button var--box var-button--large var--inline-flex var-button--primary" type="button">
    <!--v-if-->
    <div class="var-button__content"></div>
    <div class="var-hover-overlay"></div>
  </button><button class="var-button var--box var-button--large var--inline-flex var-button--primary" type="button">
    <!--v-if-->
    <div class="var-button__content"></div>
    <div class="var-hover-overlay"></div>
  </button></div>"
`;

exports[`test button group component slots > button group default slot 1`] = `
"<div class="var-button-group var--box var-button-group--mode-normal var-button-group--horizontal var-elevation--2"><button class="var-button var--box var-button--normal var--inline-flex var-button--default" type="button">
    <!--v-if-->
    <div class="var-button__content"></div>
    <div class="var-hover-overlay"></div>
  </button><button class="var-button var--box var-button--normal var--inline-flex var-button--default" type="button">
    <!--v-if-->
    <div class="var-button__content"></div>
    <div class="var-hover-overlay"></div>
  </button><button class="var-button var--box var-button--normal var--inline-flex var-button--default" type="button">
    <!--v-if-->
    <div class="var-button__content"></div>
    <div class="var-hover-overlay"></div>
  </button></div>"
`;
