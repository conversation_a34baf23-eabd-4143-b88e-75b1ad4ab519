// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`test card component slots > card description slot 1`] = `
"<div class="var-card var-elevation--1">
  <div class="var-card__floater" style="width: 100%; height: 100%; overflow: hidden;">
    <!--v-if-->
    <div class="var-card__container">
      <!--v-if-->
      <!--v-if-->
      <!--v-if--><span class="var-card__description">description</span>
      <!--v-if-->
      <!--v-if-->
    </div>
    <!--v-if-->
  </div>
  <div class="var-card__holder" style="width: auto; height: auto;"></div>
</div>"
`;

exports[`test card component slots > card extra slot 1`] = `
"<div class="var-card var-elevation--1">
  <div class="var-card__floater" style="width: 100%; height: 100%; overflow: hidden;">
    <!--v-if-->
    <div class="var-card__container">
      <!--v-if-->
      <!--v-if-->
      <!--v-if-->
      <!--v-if-->
      <div class="var-card__footer"><span>extra</span></div>
      <!--v-if-->
    </div>
    <!--v-if-->
  </div>
  <div class="var-card__holder" style="width: auto; height: auto;"></div>
</div>"
`;

exports[`test card component slots > card floating-content slot 1`] = `
"<div class="var-card var-elevation--1">
  <div class="var-card__floater" style="width: 100%; height: 100%; overflow: hidden;">
    <!--v-if-->
    <div class="var-card__container">
      <!--v-if-->
      <!--v-if-->
      <!--v-if-->
      <!--v-if-->
      <!--v-if-->
      <div class="var-card__floating-content" style="height: 0px; opacity: 0; transition: opacity 500ms;"><span>floating-content</span></div>
    </div>
    <!--v-if-->
  </div>
  <div class="var-card__holder" style="width: auto; height: auto;"></div>
</div>"
`;

exports[`test card component slots > card floating-content slot 2`] = `
"<div class="var-card var-card--layout-row var-elevation--1">
  <div class="var-card__floater" style="width: auto; height: auto; overflow: hidden;">
    <!--v-if-->
    <div class="var-card__container">
      <!--v-if-->
      <!--v-if-->
      <!--v-if-->
      <!--v-if-->
      <!--v-if-->
      <!--v-if-->
    </div>
    <!--v-if-->
  </div>
  <div class="var-card__holder" style="width: auto; height: auto;"></div>
</div>"
`;

exports[`test card component slots > card image slot 1`] = `
"<div class="var-card var-elevation--1">
  <div class="var-card__floater" style="width: 100%; height: 100%; overflow: hidden;"><img src="https://varletjs.org/varlet/cat.jpg" alt="cat">
    <div class="var-card__container">
      <!--v-if-->
      <!--v-if-->
      <!--v-if-->
      <!--v-if-->
      <!--v-if-->
      <!--v-if-->
    </div>
    <!--v-if-->
  </div>
  <div class="var-card__holder" style="width: auto; height: auto;"></div>
</div>"
`;

exports[`test card component slots > card subtitle slot 1`] = `
"<div class="var-card var-elevation--1">
  <div class="var-card__floater" style="width: 100%; height: 100%; overflow: hidden;">
    <!--v-if-->
    <div class="var-card__container">
      <!--v-if--><span class="var-card__subtitle">subtitle</span>
      <!--v-if-->
      <!--v-if-->
      <!--v-if-->
      <!--v-if-->
    </div>
    <!--v-if-->
  </div>
  <div class="var-card__holder" style="width: auto; height: auto;"></div>
</div>"
`;

exports[`test card component slots > card title slot 1`] = `
"<div class="var-card var-elevation--1">
  <div class="var-card__floater" style="width: 100%; height: 100%; overflow: hidden;">
    <!--v-if-->
    <div class="var-card__container"><span class="var-card__title">title</span>
      <!--v-if-->
      <!--v-if-->
      <!--v-if-->
      <!--v-if-->
      <!--v-if-->
    </div>
    <!--v-if-->
  </div>
  <div class="var-card__holder" style="width: auto; height: auto;"></div>
</div>"
`;
