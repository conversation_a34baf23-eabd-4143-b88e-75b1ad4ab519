:root {
  --card-padding: 0 0 15px 0;
  --card-background: var(--color-surface-container-highest);
  --card-filled-background: hsla(0, 0%, 93%, 1);
  --card-outline-color: var(--color-outline);
  --card-border-radius: 4px;
  --card-image-width: 100%;
  --card-row-image-width: 140px;
  --card-image-height: 200px;
  --card-row-height: 140px;
  --card-title-color: #333;
  --card-title-font-size: 20px;
  --card-title-padding: 0 12px;
  --card-title-margin: 15px 0 0 0;
  --card-title-row-margin: 12px 0;
  --card-content-padding: 0 16px;
  --card-content-margin: 16px 0 0 0;
  --card-content-color: var(--color-on-surface-variant);
  --card-content-font-size: 14px;
  --card-content-row-margin: 16px 0 0 0;
  --card-subtitle-color: rgba(0, 0, 0, 0.6);
  --card-subtitle-font-size: 14px;
  --card-subtitle-padding: 0 12px;
  --card-subtitle-margin: 10px 0 0 0;
  --card-subtitle-row-margin: -8px 0 0 0;
  --card-description-color: rgba(0, 0, 0, 0.6);
  --card-description-font-size: 14px;
  --card-description-margin: 20px 0 0 0;
  --card-description-padding: 0 13px;
  --card-footer-padding: 0 12px;
  --card-footer-right: 13px;
  --card-footer-bottom: 9px;
  --card-footer-margin: 30px 0 0 0;
  --card-line-height: 22px;
  --card-row-line-height: 1.5;
  --card-floating-buttons-bottom: 16px;
  --card-floating-buttons-right: 16px;
  --card-floating-buttons-color: #fff;
  --card-close-button-icon-size: 24px;
  --card-close-button-size: 56px;
  --card-close-button-primary-color: #212121;
  --card-close-button-text-color: #fff;
  --card-close-button-border-radius: 50%;
}

.var-card {
  border-radius: var(--card-border-radius);
  overflow: hidden;
  width: 100%;

  &__floater {
    display: flex;
    flex-direction: column;
    position: static;
    line-height: var(--card-line-height);
    background: var(--card-background);
    transition:
      background-color 0.25s,
      color 0.25s;
    transition-timing-function: cubic-bezier(0.45, 0.19, 0.06, 0.89);
  }

  &__container {
    padding: var(--card-padding);
    flex-grow: 1;
    min-width: 0;
  }

  &--layout-row &__floater {
    min-height: var(--card-row-height);
    height: 100%;
    flex-direction: row;
    position: relative;
    line-height: var(--card-row-line-height);
  }

  &__content {
    padding: var(--card-content-padding);
    margin: var(--card-content-margin);
    font-size: var(--card-content-font-size);
    color: var(--card-content-color);
    transition:
      padding 0.25s,
      margin 0.25s,
      font-size 0.25s;
  }

  &--layout-row &__content {
    max-width: 100%;
    margin: var(--card-title-row-margin);
  }

  &__image {
    width: var(--card-image-width);
    height: var(--card-image-height);
    display: block;
    transition: all 0.25s;
  }

  &--layout-row &__image {
    width: var(--card-row-image-width);
    height: auto;
    display: block;
    flex-shrink: 0;
  }

  &__title {
    font-size: var(--card-title-font-size);
    padding: var(--card-title-padding);
    margin: var(--card-title-margin);
    color: var(--card-title-color);
    word-break: break-word;
    transition:
      padding 0.25s,
      margin 0.25s,
      font-size 0.25s;
  }

  &--layout-row &__title {
    max-width: 100%;
    overflow: hidden;
    margin: var(--card-title-row-margin);
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__subtitle {
    font-size: var(--card-subtitle-font-size);
    color: var(--card-subtitle-color);
    padding: var(--card-subtitle-padding);
    margin: var(--card-subtitle-margin);
    word-break: break-word;
    transition:
      padding 0.25s,
      margin 0.25s,
      font-size 0.25s;
  }

  &--layout-row &__subtitle {
    margin: var(--card-subtitle-row-margin);
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }

  &__description {
    font-size: var(--card-description-font-size);
    color: var(--card-description-color);
    margin: var(--card-description-margin);
    padding: var(--card-description-padding);
    word-break: break-all;
    transition:
      padding 0.25s,
      margin 0.25s,
      font-size 0.25s;
  }

  &__footer {
    display: flex;
    justify-content: flex-end;
    padding: var(--card-footer-padding);
    margin: var(--card-footer-margin);
    transition: all 0.25s;
  }

  &--layout-row &__footer {
    position: absolute;
    padding: 0;
    right: var(--card-footer-right);
    bottom: var(--card-footer-bottom);
  }

  &__floating-content {
    overflow: hidden;
    transition-timing-function: cubic-bezier(0.45, 0.19, 0.06, 0.89);
  }

  &__floating-buttons {
    position: fixed;
    bottom: var(--card-floating-buttons-bottom);
    right: var(--card-floating-buttons-right);
    color: var(--card-floating-buttons-color);
  }

  &__close-button[var-card-cover] {
    width: var(--card-close-button-size);
    height: var(--card-close-button-size);
    background-color: var(--card-close-button-primary-color);
    color: var(--card-close-button-text-color);
    border-radius: var(--card-close-button-border-radius);
  }

  &__close-button-icon[var-card-cover] {
    font-size: var(--card-close-button-icon-size);
  }

  &--outline {
    border: thin solid var(--card-outline-color);
  }

  &--filled &__floater {
    background-color: var(--card-filled-background);
  }
}
