// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`test image preview component props > image preview imagePreventDefault 1`] = `
"<transition-stub name="var-fade" appear="false" persisted="false" css="true">
  <div class="var--box var-popup var-popup--pointer-events-none" style="z-index: 1998;">
    <!---->
    <transition-stub name="var-fade" appear="false" persisted="false" css="true">
      <div class="var-popup__content var-popup--center var-popup--content-background-color var-elevation--3 var-image-preview__popup" style="z-index: 2000;" role="dialog" aria-modal="true" var-image-preview-cover="">
        <div class="var-swipe var-image-preview__swipe" var-image-preview-cover="">
          <div class="var-swipe__track" style="width: 375px; transform: translateX(0px); transition-duration: 300ms;">
            <div class="var-swipe-item var-image-preview__swipe-item" style="width: 375px; transform: translateX(0px);" tabindex="-1" aria-hidden="false" var-image-preview-cover="">
              <div class="var-image-preview__zoom-container" style="transform: scale(1) translate(0px, 0px);"><img role="img" class="var-image-preview__image var-image-preview--prevent" src="https://varletjs.org/varlet/cat.jpg" alt="https://varletjs.org/varlet/cat.jpg"></div>
            </div>
          </div>
          <!--v-if-->
          <!--v-if-->
          <!--v-if-->
        </div>
        <!--v-if-->
        <!--v-if-->
      </div>
    </transition-stub>
  </div>
</transition-stub>"
`;

exports[`test image preview component props > image preview initialIndex 1`] = `
"<transition-stub name="var-fade" appear="false" persisted="false" css="true">
  <div class="var--box var-popup var-popup--pointer-events-none" style="z-index: 1998;">
    <!---->
    <transition-stub name="var-fade" appear="false" persisted="false" css="true">
      <div class="var-popup__content var-popup--center var-popup--content-background-color var-elevation--3 var-image-preview__popup" style="z-index: 2000;" role="dialog" aria-modal="true" var-image-preview-cover="">
        <div class="var-swipe var-image-preview__swipe" var-image-preview-cover="">
          <div class="var-swipe__track" style="width: 750px; transform: translateX(-375px); transition-duration: 300ms;">
            <div class="var-swipe-item var-image-preview__swipe-item" style="width: 375px; transform: translateX(0px);" tabindex="-1" aria-hidden="true" var-image-preview-cover="">
              <div class="var-image-preview__zoom-container" style="transform: scale(1) translate(0px, 0px);"><img role="img" class="var-image-preview__image" src="https://varletjs.org/varlet/cat.jpg" alt="https://varletjs.org/varlet/cat.jpg"></div>
            </div>
            <div class="var-swipe-item var-image-preview__swipe-item" style="width: 375px; transform: translateX(0px);" tabindex="-1" aria-hidden="false" var-image-preview-cover="">
              <div class="var-image-preview__zoom-container" style="transform: scale(1) translate(0px, 0px);"><img role="img" class="var-image-preview__image" src="https://varletjs.org/varlet/cat2.jpg" alt="https://varletjs.org/varlet/cat2.jpg"></div>
            </div>
          </div>
          <!--v-if-->
          <!--v-if-->
          <div class="var-image-preview__indicators">2 / 2</div>
        </div>
        <!--v-if-->
        <!--v-if-->
      </div>
    </transition-stub>
  </div>
</transition-stub>"
`;

exports[`test image preview component props > image preview show 1`] = `
"<transition-stub name="var-fade" appear="false" persisted="false" css="true">
  <div class="var--box var-popup var-popup--pointer-events-none" style="z-index: 1998; display: none;">
    <!---->
    <transition-stub name="var-fade" appear="false" persisted="false" css="true">
      <div class="var-popup__content var-popup--center var-popup--content-background-color var-elevation--3 var-image-preview__popup" style="z-index: 2000; display: none;" role="dialog" aria-modal="true" var-image-preview-cover="">
        <!---->
      </div>
    </transition-stub>
  </div>
</transition-stub>"
`;

exports[`test image preview component props > image preview show 2`] = `
"<transition-stub name="var-fade" appear="false" persisted="false" css="true">
  <div class="var--box var-popup var-popup--pointer-events-none" style="z-index: 1998;">
    <!---->
    <transition-stub name="var-fade" appear="false" persisted="false" css="true">
      <div class="var-popup__content var-popup--center var-popup--content-background-color var-elevation--3 var-image-preview__popup" style="z-index: 2000;" role="dialog" aria-modal="true" var-image-preview-cover="">
        <div class="var-swipe var-image-preview__swipe" var-image-preview-cover="">
          <div class="var-swipe__track" style="width: 0px; transform: translateX(0px); transition-duration: 300ms;"></div>
          <!--v-if-->
          <!--v-if-->
          <!--v-if-->
        </div>
        <!--v-if-->
        <!--v-if-->
      </div>
    </transition-stub>
  </div>
</transition-stub>"
`;
