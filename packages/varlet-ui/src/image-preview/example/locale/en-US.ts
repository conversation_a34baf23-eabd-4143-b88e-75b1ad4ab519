export default {
  functionCall: 'Function Call',
  componentCall: 'Component Call',
  preview: 'Basic usage',
  callBack: 'Handle callback function',
  basicUse: 'Basic usage',
  specifyInitialPosition: 'Specify initial position',
  displayCloseButton: 'Display the close button',
  listenCloseEvents: 'Listen for close event',
  shutdownEvent: 'The shutdown event was triggered.',
  preventLongTapDefault: 'Listen for long press event',
  preventDefaultEvent: 'Long press event is triggered',
  showExtraSlots: 'Show extra slots',
  operate: 'Operate',
}
