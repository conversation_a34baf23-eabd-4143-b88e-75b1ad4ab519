// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`test app bar component props > app bar image and image-linear-gradient 1`] = `
"<div class="var-app-bar var--box var-elevation--3" style="background-position: center center; background-size: cover; z-index: 1;">
  <div class="var-app-bar__toolbar">
    <div class="var-app-bar__left">
      <div class="var-app-bar__title"></div>
    </div>
    <!--v-if-->
    <div class="var-app-bar__right">
      <!--v-if-->
    </div>
  </div>
</div>
<!--v-if-->"
`;

exports[`test app bar slots > app bar content slot 1`] = `
"<div class="var-app-bar var--box var-elevation--3" style="z-index: 1;">
  <div class="var-app-bar__toolbar">
    <div class="var-app-bar__left">
      <div class="var-app-bar__title"></div>
    </div>
    <!--v-if-->
    <div class="var-app-bar__right">
      <!--v-if-->
    </div>
  </div>This is content slot
</div>
<!--v-if-->"
`;
