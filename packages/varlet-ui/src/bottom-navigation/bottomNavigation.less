:root {
  --bottom-navigation-height: 50px;
  --bottom-navigation-variant-height: 66px;
  --bottom-navigation-z-index: 1;
  --bottom-navigation-background-color: var(--color-surface-container-high);
  --bottom-navigation-border-color: var(--color-outline);
  --bottom-navigation-fab-offset: 4px;
  --bottom-navigation-fab-border-radius: 50%;
}

.var-bottom-navigation {
  width: 100%;
  height: var(--bottom-navigation-height);
  display: flex;
  position: relative;
  background-color: var(--bottom-navigation-background-color);
  transition:
    background-color 250ms,
    border-color 250ms;
  -webkit-tap-highlight-color: transparent;

  &--safe-area {
    padding-bottom: constant(safe-area-inset-bottom); // iOS < 11.2
    padding-bottom: env(safe-area-inset-bottom); // iOS >= 11.2
    box-sizing: content-box !important;
  }

  &--fixed {
    position: fixed;
    left: 0;
    bottom: 0;
  }

  &--border {
    border-top: 1px solid var(--bottom-navigation-border-color);
  }

  &--variant {
    height: var(--bottom-navigation-variant-height);
  }

  &__fab[var-bottom-navigation__fab] {
    width: var(--bottom-navigation-height);
    height: var(--bottom-navigation-height);
    border-radius: var(--bottom-navigation-fab-border-radius);
    position: absolute;
    z-index: 2;
    transform: translateY(-50%);
    overflow: hidden;
    transition: right 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  }

  &--fab-center {
    right: calc(50% - var(--bottom-navigation-height) / 2);
  }

  &--fab-right {
    right: var(--bottom-navigation-fab-offset);
  }
}
