:root {
  --avatar-group-offset: -10px;
}

.var-avatar-group {
  display: inline-flex;
  flex-wrap: wrap;

  &--row {
    margin-left: calc(var(--avatar-group-offset) * -1);

    .var-avatar {
      margin-left: var(--avatar-group-offset);
    }
  }

  &--column {
    flex-direction: column;
    margin-top: calc(var(--avatar-group-offset) * -1);

    .var-avatar {
      margin-top: var(--avatar-group-offset);
    }
  }
}
