export default {
  example: 'Form Example',
  username: 'Please input username',
  usernameMessage: 'The username cannot be empty',
  password: 'Please input password',
  passwordMessage: 'The password cannot be empty',
  email: 'Please input email',
  emailMessage: 'The email cannot be empty',
  passwordMinLengthMessage: 'The password cannot be less than 8 characters',
  department: 'Please select department',
  departmentMessage: 'The select cannot be empty',
  eat: 'Eat',
  sleep: 'Sleep',
  play: 'Play game',
  departmentUnit: ' department',
  group: 'Please select group',
  groupMessage: 'The select cannot be empty',
  groupUnit: ' group',
  genderMessage: 'The gender cannot be empty',
  male: 'Male',
  female: 'Female',
  rateMessage: 'It has to be greater than 2',
  likeMessage: 'The select cannot be empty',
  licenseMessage: 'You must turn on',
  countMessage: 'It has to be greater than 10',
  rangeMessage: 'It has to be greater than 10',
  filesMessage: 'Upload at least one picture',
  customFormComponent: 'Custom Form Component',
  customLabel: 'Click toggle',
  customErrorMessage: 'Please check it',
  customExtraMessage: 'Extra message',
  controller: 'Form control',
  reset: 'Empty form',
  resetValidation: 'Empty the validation',
  validate: 'Trigger validation',
  disabled: 'Form disabled',
  readonly: 'Form readonly',
}
