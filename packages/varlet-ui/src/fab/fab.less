:root {
  --fab-top: 70px;
  --fab-bottom: 16px;
  --fab-left: 16px;
  --fab-right: 16px;
  --fab-trigger-size: 56px;
  --fab-trigger-border-radius: 50%;
  --fab-trigger-inactive-icon-size: 26px;
  --fab-trigger-active-icon-size: 22px;
  --fab-actions-padding: 10px 0;
  --fab-action-margin: 6px;
  --fab-action-size: 40px;
  --fab-action-border-radius: 50%;
  --fab-transition-standard-easing: cubic-bezier(0.4, 0, 0.2, 1);
}

.var-fab-transition-default {
  &-enter-active {
    transition: 0.3s var(--fab-transition-standard-easing) !important;
  }

  &-leave-active {
    transition: 0.3s var(--fab-transition-standard-easing) !important;
  }
}

.var-fab {
  &__trigger[var-fab-cover] {
    width: var(--fab-trigger-size);
    height: var(--fab-trigger-size);
    border-radius: var(--fab-trigger-border-radius);
  }

  &__trigger-inactive-icon[var-fab-cover] {
    font-size: var(--fab-trigger-inactive-icon-size);
  }

  &__trigger-active-icon[var-fab-cover] {
    font-size: var(--fab-trigger-active-icon-size);
  }

  &__actions {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--fab-actions-padding);
  }

  &__action {
    margin: var(--fab-action-margin);

    .var-button {
      width: var(--fab-action-size);
      height: var(--fab-action-size);
      border-radius: var(--fab-action-border-radius);
    }
  }

  &--position {
    &-left-top {
      top: var(--fab-top);
      left: var(--fab-left);
    }

    &-right-top {
      top: var(--fab-top);
      right: var(--fab-right);
    }

    &-left-bottom {
      bottom: var(--fab-bottom);
      left: var(--fab-left);
    }

    &-right-bottom {
      bottom: var(--fab-bottom);
      right: var(--fab-right);
    }
  }

  &--direction {
    &-left,
    &-right {
      .var-fab__actions {
        height: 100%;
        top: 0;
        padding: 0 var(--fab-actions-padding);
      }
    }

    &-left {
      .var-fab__actions {
        flex-direction: row-reverse;
        right: 100%;
      }
    }

    &-right {
      .var-fab__actions {
        flex-direction: row;
        left: 100%;
      }
    }

    &-top,
    &-bottom {
      .var-fab__actions {
        width: 100%;
        left: 0;
      }
    }

    &-top {
      .var-fab__actions {
        flex-direction: column-reverse;
        bottom: 100%;
      }
    }

    &-bottom {
      .var-fab__actions {
        flex-direction: column;
        top: 100%;
      }
    }
  }

  &--fixed {
    position: fixed;
  }

  &--absolute {
    position: absolute;
  }

  &--active-transition {
    .var-fab-transition-default();

    &-enter-from,
    &-leave-to {
      transform: scale(0);
    }
  }

  &--actions-transition-top {
    .var-fab-transition-default();

    &-enter-from,
    &-leave-to {
      opacity: 0;
      transform: translateY(40px);
    }
  }

  &--actions-transition-bottom {
    .var-fab-transition-default();

    &-enter-from,
    &-leave-to {
      opacity: 0;
      transform: translateY(-40px);
    }
  }

  &--actions-transition-left {
    .var-fab-transition-default();

    &-enter-from,
    &-leave-to {
      opacity: 0;
      transform: translateX(40px);
    }
  }

  &--actions-transition-right {
    .var-fab-transition-default();

    &-enter-from,
    &-leave-to {
      opacity: 0;
      transform: translateX(-40px);
    }
  }

  &--trigger-icon-animation {
    transform: scale(0.4);
    opacity: 0;
    transition-property: transform, opacity;
  }

  &--safe-area {
    margin-bottom: constant(safe-area-inset-bottom); // iOS < 11.2
    margin-bottom: env(safe-area-inset-bottom); // iOS >= 11.2
  }
}
