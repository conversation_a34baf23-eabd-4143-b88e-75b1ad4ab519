export default {
  type: 'Theme Color Button',
  trigger: 'Trigger Method',
  direction: 'Action Menu Popup Direction',
  position: 'Trigger Position',
  default: 'default',
  primary: 'primary',
  info: 'info',
  success: 'success',
  warning: 'warning',
  danger: 'danger',
  drag: 'Drag',
  disabled: 'Disabled',
  elevation: 'Elevation Effect',
  triggerToggle: 'Trigger Show/Hide',
  actionsToggle: 'Action Menu Expand/Collapse',
  toggle: 'Toggle',
}
