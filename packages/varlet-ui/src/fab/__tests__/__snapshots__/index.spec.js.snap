// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`fab color 1`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" style="background: yellow;" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"></div></div></div>"`;

exports[`fab custom icon 1`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-check var-fab__trigger-inactive-icon" style="transition-duration: 200ms; font-size: 30px;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"><div class="var-fab__action"><div>action</div></div></div></div></div>"`;

exports[`fab custom icon 2`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-fire var-fab__trigger-active-icon" style="transition-duration: 200ms; font-size: 30px;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style=""><div class="var-fab__action"><div>action</div></div></div></div></div>"`;

exports[`fab direction 1`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"></div></div></div>"`;

exports[`fab direction 2`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-right"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"></div></div></div>"`;

exports[`fab direction 3`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-bottom"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"></div></div></div>"`;

exports[`fab direction 4`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-left"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"></div></div></div>"`;

exports[`fab drag 1`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"></div></div></div>"`;

exports[`fab drag 2`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"></div></div></div>"`;

exports[`fab events and fragment default slots and click outside close 1`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"><div class="var-fab__action"><div>action</div></div><div class="var-fab__action"><div>action</div></div><div class="var-fab__action"><div>action</div></div></div></div></div>"`;

exports[`fab fixed equals false 1`] = `"<div class="var-drag var--box var-fab--position-right-bottom var-fab--absolute" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"></div></div></div>"`;

exports[`fab position 1`] = `"<div class="var-drag var--box var-fab--position-left-top" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"></div></div></div>"`;

exports[`fab position 2`] = `"<div class="var-drag var--box var-fab--position-right-top" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"></div></div></div>"`;

exports[`fab position 3`] = `"<div class="var-drag var--box var-fab--position-left-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"></div></div></div>"`;

exports[`fab position 4`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"></div></div></div>"`;

exports[`fab safeArea 1`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top var-fab--safe-area"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"></div></div></div>"`;

exports[`fab teleport 1`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"><div class="var-fab__action"><div>action</div></div></div></div></div>"`;

exports[`fab trigger equals click 1`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"><div class="var-fab__action"><div>action</div></div></div></div></div>"`;

exports[`fab trigger equals click 2`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-window-close var-fab__trigger-active-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style=""><div class="var-fab__action"><div>action</div></div></div></div></div>"`;

exports[`fab trigger equals click 3`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"><div class="var-fab__action"><div>action</div></div></div></div></div>"`;

exports[`fab trigger equals hover 1`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"><div class="var-fab__action"><div>action</div></div></div></div></div>"`;

exports[`fab trigger equals hover 2`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-window-close var-fab__trigger-active-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style=""><div class="var-fab__action"><div>action</div></div></div></div></div>"`;

exports[`fab trigger equals hover 3`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"><div class="var-fab__action"><div>action</div></div></div></div></div>"`;

exports[`fab trigger slot 1`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><div>trigger</div><div class="var-fab__actions" style="display: none;"></div></div></div>"`;

exports[`fab type 1`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-default var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"></div></div></div>"`;

exports[`fab type 2`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"></div></div></div>"`;

exports[`fab type 3`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-warning var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"></div></div></div>"`;

exports[`fab type 4`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-info var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"></div></div></div>"`;

exports[`fab type 5`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-danger var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"></div></div></div>"`;

exports[`fab type 6`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 90;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-success var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"></div></div></div>"`;

exports[`fab zIndex and inset 1`] = `"<div class="var-drag var--box var-fab--position-right-bottom" style="z-index: 999; top: 10px; bottom: 10px; left: 10px; right: 10px;"><div class="var-fab var-fab--direction-top"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--icon-container-primary var-fab__trigger" type="button" var-fab-cover="true"><!--v-if--><div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus var-fab__trigger-inactive-icon" style="transition-duration: 200ms;" var-fab-cover="true"></i></div><div class="var-hover-overlay"></div></button><div class="var-fab__actions" style="display: none;"></div></div></div>"`;
