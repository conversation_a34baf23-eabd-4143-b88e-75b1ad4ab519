:root {
  --collapse-background: var(--color-surface-container-highest);
  --collapse-text-color: #232222;
  --collapse-header-font-size: var(--font-size-lg);
  --collapse-header-padding: 10px 12px;
  --collapse-content-font-size: var(--font-size-md);
  --collapse-content-padding: 0 12px 10px;
  --collapse-item-margin-top: 16px;
  --collapse-disable-color: #bdbdbd;
  --collapse-border-top: thin solid var(--color-outline);
}

.var-collapse-item {
  -webkit-tap-highlight-color: transparent;
  box-sizing: border-box;
  position: relative;
  margin-top: 0;
  background: var(--collapse-background);
  color: var(--collapse-text-color);
  transition:
    margin-top 0.25s,
    background-color 0.25s;

  &__shadow {
    bottom: 0;
    content: '';
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    z-index: -1;
  }

  &:not(:first-child)::after {
    border-top: var(--collapse-divider-top);
    content: '';
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
  }

  &__header {
    align-items: center;
    display: flex;
    font-size: var(--collapse-header-font-size);
    outline: none;
    padding: var(--collapse-header-padding);
    justify-content: space-between;
    position: relative;

    &-title {
      transition: color 0.25s;
    }

    &-icon {
      transform: rotate(0deg);
      opacity: 1;
    }

    &-open {
      transform: rotate(-180deg);
    }

    &--disable {
      opacity: 0;
    }

    &--cursor-pointer {
      cursor: pointer;
    }
  }

  &__content {
    font-size: var(--collapse-content-font-size);
    overflow: hidden;
    transition: all 0.25s;

    &-wrap {
      padding: var(--collapse-content-padding);
      word-break: break-all;
    }
  }

  &--active + &,
  &--active:not(:first-child) {
    margin-top: var(--collapse-item-margin-top);

    &::after {
      border-top: none;
    }
  }

  &--disable {
    color: var(--collapse-disable-color);
    cursor: not-allowed;
  }
}
