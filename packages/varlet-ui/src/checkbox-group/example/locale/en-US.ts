export default {
  basicUsage: 'Basic Usage',
  currentValue: 'Current value:',
  setState: 'Set State Value',
  setStyle: 'Modify The Icon And Color',
  disabled: 'Disabled',
  readonly: 'Readonly',
  eat: 'Eat',
  sleep: 'Sleep',
  game: 'Game',
  checkAll: 'Check All',
  inverseAll: 'Inverse All',
  checkboxGroup: 'CheckboxGroup',
  checkboxGroupOptions: 'Options API',
  customFields: 'Custom Fields',
  vertical: 'Vertical Direction',
  checkboxValidate: 'Checkbox Validation',
  checkboxValidateWithZod: 'Checkbox Validation With Zod',
  checkboxGroupValidate: 'CheckboxGroup Validate',
  checkboxGroupValidateWithZod: 'CheckboxGroup Validate with <PERSON><PERSON>',
  checkboxValidateMessage: 'Please check your choices',
  checkboxGroupValidateMessage: 'Please check all',
  indeterminate: 'Indeterminate',
  indeterminateValue: 'Current value:',
  toggle: 'Toggle',
  max: 'Maximum Number Of Checked',
}
