// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`test code component props > code content 1`] = `
"<div class="var-highlighter-provider">
  <div class="var-code"><pre class="shiki vitesse-light" style="background-color:#ffffff;color:#393a34" tabindex="0"><code><span class="line"><span style="color:#B07D48">console</span><span style="color:#999999">.</span><span style="color:#59873A">log</span><span style="color:#999999">(</span><span style="color:#B5695977">'</span><span style="color:#B56959">hello world</span><span style="color:#B5695977">'</span><span style="color:#999999">)</span></span></code></pre>
  </div>
</div>"
`;

exports[`test code component props > code language 1`] = `
"<div class="var-highlighter-provider">
  <div class="var-code"><pre class="shiki vitesse-light" style="background-color:#ffffff;color:#393a34" tabindex="0"><code><span class="line"><span style="color:#998418">print</span><span style="color:#999999">(</span><span style="color:#B5695977">'</span><span style="color:#B56959">Hello Varlet UI</span><span style="color:#B5695977">'</span><span style="color:#999999">)</span></span></code></pre>
  </div>
</div>"
`;

exports[`test code component props > code no trim 1`] = `
"<div class="var-highlighter-provider">
  <div class="var-code"><pre class="shiki vitesse-light" style="background-color:#ffffff;color:#393a34" tabindex="0"><code><span class="line"><span style="color:#B07D48">   console</span><span style="color:#999999">.</span><span style="color:#59873A">log</span><span style="color:#999999">(</span><span style="color:#B5695977">'</span><span style="color:#B56959">hello world</span><span style="color:#B5695977">'</span><span style="color:#999999">)</span><span style="color:#393A34">   </span></span></code></pre>
  </div>
</div>"
`;

exports[`test code component props > code theme 1`] = `
"<div class="var-highlighter-provider">
  <div class="var-code"><pre class="shiki material-theme" style="background-color:#263238;color:#EEFFFF" tabindex="0"><code><span class="line"><span style="color:#EEFFFF">console</span><span style="color:#89DDFF">.</span><span style="color:#82AAFF">log</span><span style="color:#EEFFFF">(</span><span style="color:#89DDFF">'</span><span style="color:#C3E88D">hello world</span><span style="color:#89DDFF">'</span><span style="color:#EEFFFF">)</span></span></code></pre>
  </div>
</div>"
`;

exports[`test code component props > code trim 1`] = `
"<div class="var-highlighter-provider">
  <div class="var-code"><pre class="shiki vitesse-light" style="background-color:#ffffff;color:#393a34" tabindex="0"><code><span class="line"><span style="color:#B07D48">console</span><span style="color:#999999">.</span><span style="color:#59873A">log</span><span style="color:#999999">(</span><span style="color:#B5695977">'</span><span style="color:#B56959">hello world</span><span style="color:#B5695977">'</span><span style="color:#999999">)</span></span></code></pre>
  </div>
</div>"
`;
