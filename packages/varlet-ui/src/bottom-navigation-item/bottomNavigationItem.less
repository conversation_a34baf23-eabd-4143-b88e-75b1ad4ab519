:root {
  --bottom-navigation-item-font-size: var(--font-size-sm);
  --bottom-navigation-item-inactive-color: #646566;
  --bottom-navigation-item-active-color: var(--color-primary);
  --bottom-navigation-item-active-background-color: var(--color-surface-container-high);
  --bottom-navigation-item-variant-active-background-color: var(--color-primary-container);
  --bottom-navigation-item-variant-active-color: var(--color-on-primary-container);
  --bottom-navigation-item-line-height: 1;
  --bottom-navigation-item-icon-size: 22px;
  --bottom-navigation-item-icon-margin-bottom: 5px;
  --bottom-navigation-item-variant-icon-container-height: 30px;
  --bottom-navigation-item-variant-icon-container-border-radius: 100px;
  --bottom-navigation-item-variant-icon-container-max-width: 58px;
}

.var-bottom-navigation-item {
  height: 100%;
  padding-bottom: 2px;
  position: relative;
  display: inline-flex;
  flex: 1 1 0%;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  line-height: var(--bottom-navigation-item-line-height);
  color: var(--bottom-navigation-item-inactive-color);
  cursor: pointer;
  user-select: none;
  vertical-align: middle;
  appearance: none;
  text-decoration: none;
  background-color: transparent;
  outline: 0;
  border: 0;
  transition:
    color 250ms,
    margin 250ms;

  &--variant-padding {
    padding-bottom: 6px;
  }

  &--active {
    color: var(--bottom-navigation-item-active-color);
    background-color: var(--bottom-navigation-item-active-background-color);
    transition: background-color 250ms;

    .var-bottom-navigation-item__label {
      font-size: calc(var(--bottom-navigation-item-font-size) * 1.16);
    }
  }

  &--variant-icon-container {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 0;
    height: var(--bottom-navigation-item-variant-icon-container-height);
    border-radius: var(--bottom-navigation-item-variant-icon-container-border-radius);
    max-width: var(--bottom-navigation-item-variant-icon-container-max-width);
  }

  &--variant-active {
    color: var(--bottom-navigation-item-variant-active-color);
    background-color: var(--bottom-navigation-item-variant-active-background-color);
    width: 100%;
    transition:
      background-color 0.25s,
      width 0.5s;
  }

  &--right-half-space {
    margin-right: calc(var(--bottom-navigation-height) / 2);
  }

  &--left-half-space {
    margin-left: calc(var(--bottom-navigation-height) / 2);
  }

  &--right-space {
    margin-right: calc(var(--bottom-navigation-height) + var(--bottom-navigation-fab-offset));
  }

  &__icon[var-bottom-navigation-item-cover] {
    font-size: var(--bottom-navigation-item-icon-size);
  }

  &__badge[var-bottom-navigation-item-cover] {
    margin-top: 6px;
    margin-right: -4px;
  }

  &__label {
    margin-top: var(--bottom-navigation-item-icon-margin-bottom);
    font-size: var(--bottom-navigation-item-font-size);
    transition: font-size 0.2s ease 0.1s;
    white-space: nowrap;
  }
}
