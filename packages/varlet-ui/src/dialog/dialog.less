:root {
  --dialog-width: 280px;
  --dialog-border-radius: 3px;
  --dialog-title-padding: 20px 20px 0;
  --dialog-message-color: #888;
  --dialog-message-padding: 12px 20px;
  --dialog-message-line-height: 24px;
  --dialog-message-font-size: var(--font-size-md);
  --dialog-title-font-size: var(--font-size-lg);
  --dialog-actions-padding: 0 12px 12px;
  --dialog-button-margin-left: 6px;
  --dialog-title-color: #555;
  --dialog-confirm-button-color: var(--color-primary);
  --dialog-cancel-button-color: var(--color-primary);
  --dialog-background: var(--color-surface-container-low);
}

.var-dialog {
  width: var(--dialog-width);
  background: var(--dialog-background);
  transition: 0.25s background-color;

  &__popup[var-dialog-cover] {
    background: transparent;
    border-radius: var(--dialog-border-radius);
  }

  &__title {
    font-size: var(--dialog-title-font-size);
    font-weight: 400;
    padding: var(--dialog-title-padding);
    color: var(--dialog-title-color);
  }

  &__message {
    padding: var(--dialog-message-padding);
    color: var(--dialog-message-color);
    line-height: var(--dialog-message-line-height);
    font-size: var(--dialog-message-font-size);
    word-wrap: break-word;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    padding: var(--dialog-actions-padding);
  }

  &__button[var-dialog-cover] {
    margin-left: var(--dialog-button-margin-left);
    background-color: transparent;
    box-shadow: none;

    &:active {
      box-shadow: none;
    }
  }

  &__confirm-button[var-dialog-cover] {
    color: var(--dialog-confirm-button-color);
  }

  &__cancel-button[var-dialog-cover] {
    color: var(--dialog-cancel-button-color);
  }
}
