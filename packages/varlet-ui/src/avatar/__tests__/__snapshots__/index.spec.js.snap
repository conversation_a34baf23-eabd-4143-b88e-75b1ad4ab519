// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`avatar group basic use 1`] = `
"<div class="var-avatar-group var-avatar-group--row">
  <div class="var-avatar var--box var-avatar--normal var-avatar--round">
    <div class="var-avatar__text" style="transform: scale(1);"></div>
  </div>
  <div class="var-avatar var--box var-avatar--normal var-avatar--round">
    <div class="var-avatar__text" style="transform: scale(1);"></div>
  </div>
  <div class="var-avatar var--box var-avatar--normal var-avatar--round">
    <div class="var-avatar__text" style="transform: scale(1);"></div>
  </div>
</div>"
`;

exports[`avatar group offset 1`] = `
"<div class="var-avatar-group var-avatar-group--row" style="--avatar-group-offset: 10px;">
  <div class="var-avatar var--box var-avatar--normal var-avatar--round">
    <div class="var-avatar__text" style="transform: scale(1);"></div>
  </div>
  <div class="var-avatar var--box var-avatar--normal var-avatar--round">
    <div class="var-avatar__text" style="transform: scale(1);"></div>
  </div>
  <div class="var-avatar var--box var-avatar--normal var-avatar--round">
    <div class="var-avatar__text" style="transform: scale(1);"></div>
  </div>
</div>"
`;

exports[`avatar group vertical 1`] = `
"<div class="var-avatar-group var-avatar-group--column">
  <div class="var-avatar var--box var-avatar--normal var-avatar--round">
    <div class="var-avatar__text" style="transform: scale(1);"></div>
  </div>
  <div class="var-avatar var--box var-avatar--normal var-avatar--round">
    <div class="var-avatar__text" style="transform: scale(1);"></div>
  </div>
  <div class="var-avatar var--box var-avatar--normal var-avatar--round">
    <div class="var-avatar__text" style="transform: scale(1);"></div>
  </div>
</div>"
`;

exports[`avatar src 1`] = `"<div class="var-avatar var--box var-avatar--normal var-avatar--round"><img role="img" class="var-avatar__image" src="https://1.png" style="object-fit: cover;"></div>"`;

exports[`avatar src and alt 1`] = `"<div class="var-avatar var--box var-avatar--normal var-avatar--round"><img role="img" class="var-avatar__image" src="https://1.png" alt="1.png" style="object-fit: cover;"></div>"`;

exports[`avatar src and fit 1`] = `"<div class="var-avatar var--box var-avatar--normal var-avatar--round"><img role="img" class="var-avatar__image" src="https://1.png" style="object-fit: fill;"></div>"`;

exports[`avatar src and fit 2`] = `"<div class="var-avatar var--box var-avatar--normal var-avatar--round"><img role="img" class="var-avatar__image" src="https://1.png" style="object-fit: contain;"></div>"`;

exports[`avatar src and fit 3`] = `"<div class="var-avatar var--box var-avatar--normal var-avatar--round"><img role="img" class="var-avatar__image" src="https://1.png" style="object-fit: cover;"></div>"`;

exports[`avatar src and fit 4`] = `"<div class="var-avatar var--box var-avatar--normal var-avatar--round"><img role="img" class="var-avatar__image" src="https://1.png" style="object-fit: none;"></div>"`;

exports[`avatar src and fit 5`] = `"<div class="var-avatar var--box var-avatar--normal var-avatar--round"><img role="img" class="var-avatar__image" src="https://1.png" style="object-fit: scale-down;"></div>"`;
