:root {
  --index-bar-list-right: 0;
  --index-bar-list-top: 50%;
  --index-bar-list-left: auto;
  --index-bar-list-bottom: auto;
  --index-bar-list-transform: translate(0, -50%);
  --index-bar-list-item-font-size: var(--font-size-xs);
  --index-bar-list-item-color: var(--color-primary);
  --index-bar-list-item-active-color: var(--color-danger);
  --index-bar-list-item-height: 14px;
  --index-bar-list-item-padding: 0 10px;
}

.var-index-bar {
  position: relative;

  &__anchor-list {
    position: fixed;
    right: var(--index-bar-list-right);
    top: var(--index-bar-list-top);
    left: var(--index-bar-list-left);
    bottom: var(--index-bar-list-bottom);
    transform: var(--index-bar-list-transform);
    list-style: none;
    padding: 0;
    margin: 0;
  }

  &__anchor-item {
    font-size: var(--index-bar-list-item-font-size);
    display: flex;
    align-items: center;
    height: var(--index-bar-list-item-height);
    justify-content: center;
    padding: var(--index-bar-list-item-padding);
    color: var(--index-bar-list-item-color);
    cursor: pointer;
    -webkit-tap-highlight-color: transparent;

    &--active {
      color: var(--index-bar-list-item-active-color);
    }
  }
}
