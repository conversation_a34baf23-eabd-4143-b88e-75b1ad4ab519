.var-col {
  transition: all 0.25s;
  display: flex !important;

  &--span-0 {
    display: none !important;
  }
}

.create-col(@i) when (@i =< 24) {
  .var-col--span-@{i} {
    flex: 0 0 @i * (100% / 24);
    max-width: @i * (100% / 24);
  }

  .var-col--offset-@{i} {
    margin-left: @i * (100% / 24);
  }

  .create-col(@i + 1);
}

.create-responsive-col(@i, @mode) when (@i =< 24) {
  .var-col--span-@{mode}-@{i} {
    flex: 0 0 @i * (100% / 24);
    max-width: @i * (100% / 24);
  }

  .var-col--offset-@{mode}-@{i} {
    margin-left: @i * (100% / 24);
  }

  .create-responsive-col(@i + 1, @mode);
}

.create-col(1);

@media only screen and (max-width: 767px) {
  .create-responsive-col(1, xs);

  .var-col--span-xs-0 {
    display: none !important;
  }
}

@media only screen and (min-width: 768px) and (max-width: 991px) {
  .create-responsive-col(1, sm);

  .var-col--span-sm-0 {
    display: none !important;
  }
}

@media only screen and (min-width: 992px) and (max-width: 1199px) {
  .create-responsive-col(1, md);

  .var-col--span-md-0 {
    display: none !important;
  }
}

@media only screen and (min-width: 1200px) and (max-width: 1919px) {
  .create-responsive-col(1, lg);

  .var-col--span-lg-0 {
    display: none !important;
  }
}

@media only screen and (min-width: 1920px) {
  .create-responsive-col(0, xl);

  .var-col--span-xl-0 {
    display: none !important;
  }
}
