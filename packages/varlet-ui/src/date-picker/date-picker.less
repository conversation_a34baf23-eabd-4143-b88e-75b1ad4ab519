:root {
  --date-picker-border-radius: 4px;
  --date-picker-font-size: var(--font-size-md);
  --date-picker-min-width: 290px;
  --date-picker-main-color: rgba(0, 0, 0, 0.87);
  --date-picker-title-hint-color: #fff;
  --date-picker-title-hint-font-size: var(--font-size-md);
  --date-picker-title-height: 105px;
  --date-picker-title-padding: 16px;
  --date-picker-title-background: var(--color-primary);
  --date-picker-title-color: #fff;
  --date-picker-title-year-font-size: var(--font-size-md);
  --date-picker-title-year-font-weight: 500;
  --date-picker-title-year-margin-bottom: 8px;
  --date-picker-title-year-min-height: 21px;
  --date-picker-title-date-height: 48px;
  --date-picker-title-date-font-size: 34px;
  --date-picker-title-date-font-weight: 500;
  --date-picker-title-date-range-font-size: 20px;
  --date-picker-title-date-justify-content: normal;
  --date-picker-body-background-color: #fff;
  --date-picker-body-height: 280px;
  --date-picker-body-padding: 0;
  --date-picker-header-padding: 4px 16px;
  --date-picker-header-color: #555;
  --date-picker-actions-padding: 0 8px 12px 8px;
  --date-picker-header-arrow-filter: opacity(0.54);
  --month-picker-padding: 0 12px;
  --month-picker-item-width: 33%;
  --month-picker-item-height: 56px;
  --month-picker-item-button-max-width: 140px;
  --year-picker-padding: 0 12px;
  --year-picker-item-width: 33%;
  --year-picker-item-height: 56px;
  --year-picker-item-button-max-width: 140px;
  --day-picker-content-item-width: 14.28%;
  --day-picker-content-item-min-height: 21px;
  --day-picker-content-item-font-size: var(--font-size-sm);
  --day-picker-content-item-padding: 2px 0;
  --day-picker-content-item-button-width: 32px;
  --day-picker-content-item-button-height: 32px;
  --day-picker-content-item-button-font-size: var(--font-size-sm);
  --day-picker-head-item-color: rgba(0, 0, 0, 0.38);
  --day-picker-head-item-padding: 8px 0;
  --day-picker-head-item-font-weight: 600;
}

.var-date-picker {
  -webkit-tap-highlight-color: transparent;
  border-radius: var(--date-picker-border-radius);
  contain: layout style;
  display: flex;
  width: 100%;
  flex-direction: column;
  font-size: var(--date-picker-font-size);
  position: relative;
  min-width: var(--date-picker-min-width);
  overflow: hidden;

  ul {
    list-style: none;
    margin: 0;
    padding: 0;
  }

  &__title {
    min-height: var(--date-picker-title-height);
    padding: var(--date-picker-title-padding);
    color: var(--date-picker-title-color);
    background: var(--date-picker-title-background);
    display: flex;
    justify-content: space-between;
    flex-direction: column;
    flex-wrap: wrap;

    &-select {
      display: inline-flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--date-picker-title-year-margin-bottom);
      min-height: var(--date-picker-title-year-min-height);
    }

    &-hint {
      color: var(--date-picker-title-hint-color);
      font-size: var(--date-picker-title-hint-font-size);
    }

    &-year {
      cursor: pointer;
      -webkit-tap-highlight-color: transparent;
      font-size: var(--date-picker-title-year-font-size);
      font-weight: var(--date-picker-title-year-font-weight);
      transition: 0.3s var(--cubic-bezier);
      margin-bottom: 0;

      &--active {
        opacity: 1;
      }
    }

    &-date {
      height: var(--date-picker-title-date-height);
      overflow: hidden;
      font-size: var(--date-picker-title-date-font-size);
      text-align: left;
      font-weight: var(--date-picker-title-date-font-weight);
      position: relative;
      cursor: pointer;
      -webkit-tap-highlight-color: transparent;
      display: flex;
      align-items: center;
      justify-content: var(--date-picker-title-date-justify-content);
      opacity: 0.6;
      transition: 0.3s var(--cubic-bezier);

      &--active {
        opacity: 1;
      }

      &--range {
        font-size: var(--date-picker-title-date-range-font-size);
      }
    }
  }

  &__body {
    position: relative;
    overflow: auto;
    height: var(--date-picker-body-height);
    background-color: var(--date-picker-body-background-color);
    padding: var(--date-picker-body-padding);

    &::-webkit-scrollbar {
      display: none;
      width: 0;
      background: transparent;
    }
  }

  &__actions {
    background-color: var(--date-picker-body-background-color);
    padding: var(--date-picker-actions-padding);
    display: flex;
    justify-content: flex-end;
  }

  &-header {
    padding: var(--date-picker-header-padding);
    align-items: center;
    display: flex;
    justify-content: space-between;
    position: relative;
    color: var(--date-picker-header-color);
    background-color: var(--date-picker-body-background-color);

    &__value {
      flex: 1;
      text-align: center;
      position: relative;
      overflow: hidden;
      font-weight: 700;
      cursor: pointer;
      user-select: none;

      div {
        width: 100%;
      }
    }

    &__arrow[var-date-picker-header-cover] {
      filter: var(--date-picker-header-arrow-filter);
    }
  }

  .var-month-picker {
    &__content {
      padding: var(--month-picker-padding);

      ul {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        li {
          width: var(--month-picker-item-width);
          display: flex;
          height: var(--month-picker-item-height);
          align-items: center;
          justify-content: center;
        }
      }
    }

    &__button[var-month-picker-cover] {
      width: 100%;
      max-width: var(--month-picker-item-button-max-width);
    }

    &__button[var-date-picker-color-cover='true'] {
      color: var(--date-picker-main-color);
    }

    &__button--disabled {
      color: var(--color-text-disabled) !important;
      cursor: not-allowed;
    }
  }

  .var-year-picker {
    padding: var(--year-picker-padding);
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;

    li {
      width: var(--year-picker-item-width);
      display: flex;
      height: var(--year-picker-item-height);
      align-items: center;
      justify-content: center;
    }

    &__button[var-year-picker-cover] {
      width: 100%;
      max-width: var(--year-picker-item-button-max-width);
    }

    &__button[var-date-picker-color-cover='true'] {
      color: var(--date-picker-main-color);
    }

    &__button--disabled {
      color: var(--color-text-disabled) !important;
      cursor: not-allowed;
    }
  }

  .var-day-picker {
    &__content {
      ul {
        display: flex;
        flex-wrap: wrap;

        li {
          position: relative;
          display: flex;
          justify-content: center;
          width: var(--day-picker-content-item-width);
          font-size: var(--day-picker-content-item-font-size);
          padding: var(--day-picker-content-item-padding);
          min-height: var(--day-picker-content-item-min-height);
        }
      }
    }

    &__head {
      li {
        color: var(--day-picker-head-item-color);
        padding: var(--day-picker-head-item-padding);
        font-weight: var(--day-picker-head-item-font-weight);
      }
    }

    &__button[var-day-picker-cover] {
      width: var(--day-picker-content-item-button-width);
      height: var(--day-picker-content-item-button-height);
      font-size: var(--day-picker-content-item-button-font-size);
    }

    &__button:not(.var-day-picker__button--usable) {
      cursor: unset;
    }

    &__button[var-date-picker-color-cover='true'] {
      color: var(--date-picker-main-color);
    }

    &__button--disabled {
      color: var(--color-text-disabled) !important;
      cursor: not-allowed;
    }
  }

  &-translatey-enter-from {
    opacity: 0;
    transform: translateY(100%);
  }

  &-translatey-enter-active,
  &-reverse-translatey-enter-active {
    transition: 0.3s var(--cubic-bezier);
  }

  &-translatey-leave-active,
  &-reverse-translatey-leave-active {
    position: absolute;
    transition: 0.3s var(--cubic-bezier);
  }

  &-translatey-leave-to {
    opacity: 0;
    transform: translateY(-100%);
  }

  &-reverse-translatey-enter-from {
    opacity: 0;
    transform: translateY(-100%);
  }

  &-reverse-translatey-leave-to {
    opacity: 0;
    transform: translateY(100%);
  }

  &-panel-fade-enter-from,
  &-panel-fade-leave-to {
    transition: 0.3s var(--cubic-bezier);
    opacity: 0;
  }

  &-panel-fade-leave-active {
    position: absolute;
  }

  &-translatex-enter-from {
    opacity: 0;
    transform: translateX(100%);
  }

  &-reverse-translatex-enter-from {
    opacity: 0;
    transform: translateX(-100%);
  }

  &-translatex-enter-active,
  &-reverse-translatex-enter-active {
    transition: 0.3s var(--cubic-bezier);
  }

  &-translatex-leave-active,
  &-reverse-translatex-leave-active {
    position: absolute;
    transition: 0.3s var(--cubic-bezier);
  }

  &-translatex-leave-to {
    opacity: 0;
    transform: translateX(-100%);
  }

  &-reverse-translatex-leave-to {
    opacity: 0;
    transform: translateX(100%);
  }
}
