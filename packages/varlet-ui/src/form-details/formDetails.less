:root {
  --form-details-error-message-color: var(--color-danger);
  --form-details-extra-message-color: #888;
  --form-details-margin-top: 6px;
  --form-details-font-size: 12px;
  --form-details-message-margin-right: 4px;
}

.var-form-details {
  display: flex;
  justify-content: space-between;
  font-size: var(--form-details-font-size);
  margin-top: var(--form-details-margin-top);

  &-enter-from,
  &-leave-to {
    opacity: 0;
    margin-top: 2px !important;
  }

  &-enter-active,
  &-leave-active {
    transition: 0.2s all var(--cubic-bezier);
  }

  &__message-enter-from,
  &__message-leave-to {
    opacity: 0;
  }

  &__message-enter-active,
  &__message-leave-active {
    transition: 0.2s all var(--cubic-bezier);
  }

  &__error-message {
    flex-grow: 1;
    color: var(--form-details-error-message-color);
    margin-right: var(--form-details-message-margin-right);
    user-select: none;
    text-align: left;
  }

  &__extra-message {
    flex-shrink: 0;
    color: var(--form-details-extra-message-color);
    user-select: none;
    text-align: right;
  }
}
