// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`counter decimalLength 1`] = `
"<div class="var-counter var--box">
  <div class="var-counter__controller var-elevation--2"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--default var-button--round var-counter__decrement-button" type="button" var-counter-cover="">
      <!--v-if-->
      <div class="var-button__content"><i class="var-icon var-icon--set var-icon-minus" style="transition-duration: 0ms;"></i></div>
      <div class="var-hover-overlay"></div>
    </button><input class="var-counter__input" inputmode="numeric"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--default var-button--round var-counter__increment-button" type="button" var-counter-cover="">
      <!--v-if-->
      <div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus" style="transition-duration: 0ms;"></i></div>
      <div class="var-hover-overlay"></div>
    </button></div>
  <transition-stub name="var-form-details" appear="false" persisted="false" css="true">
    <!--v-if-->
  </transition-stub>
</div>"
`;

exports[`counter decimalLength 2`] = `
"<div class="var-counter var--box">
  <div class="var-counter__controller var-elevation--2"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--default var-button--round var-counter__decrement-button" type="button" var-counter-cover="">
      <!--v-if-->
      <div class="var-button__content"><i class="var-icon var-icon--set var-icon-minus" style="transition-duration: 0ms;"></i></div>
      <div class="var-hover-overlay"></div>
    </button><input class="var-counter__input" inputmode="decimal"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--default var-button--round var-counter__increment-button" type="button" var-counter-cover="">
      <!--v-if-->
      <div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus" style="transition-duration: 0ms;"></i></div>
      <div class="var-hover-overlay"></div>
    </button></div>
  <transition-stub name="var-form-details" appear="false" persisted="false" css="true">
    <!--v-if-->
  </transition-stub>
</div>"
`;

exports[`counter decrementButton 1`] = `
"<div class="var-counter var--box">
  <div class="var-counter__controller var-elevation--2"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--default var-button--round var-counter__decrement-button" type="button" var-counter-cover="">
      <!--v-if-->
      <div class="var-button__content"><i class="var-icon var-icon--set var-icon-minus" style="transition-duration: 0ms;"></i></div>
      <div class="var-hover-overlay"></div>
    </button><input class="var-counter__input" inputmode="numeric"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--default var-button--round var-counter__increment-button" type="button" var-counter-cover="">
      <!--v-if-->
      <div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus" style="transition-duration: 0ms;"></i></div>
      <div class="var-hover-overlay"></div>
    </button></div>
  <transition-stub name="var-form-details" appear="false" persisted="false" css="true">
    <!--v-if-->
  </transition-stub>
</div>"
`;

exports[`counter decrementButton 2`] = `
"<div class="var-counter var--box">
  <div class="var-counter__controller var-elevation--2"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--default var-button--round var-counter__decrement-button var-counter--hidden" type="button" var-counter-cover="">
      <!--v-if-->
      <div class="var-button__content"><i class="var-icon var-icon--set var-icon-minus" style="transition-duration: 0ms;"></i></div>
      <div class="var-hover-overlay"></div>
    </button><input class="var-counter__input" inputmode="numeric"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--default var-button--round var-counter__increment-button" type="button" var-counter-cover="">
      <!--v-if-->
      <div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus" style="transition-duration: 0ms;"></i></div>
      <div class="var-hover-overlay"></div>
    </button></div>
  <transition-stub name="var-form-details" appear="false" persisted="false" css="true">
    <!--v-if-->
  </transition-stub>
</div>"
`;

exports[`counter elevation 1`] = `
"<div class="var-counter var--box">
  <div class="var-counter__controller var-elevation--2"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--default var-button--round var-counter__decrement-button" type="button" var-counter-cover="">
      <!--v-if-->
      <div class="var-button__content"><i class="var-icon var-icon--set var-icon-minus" style="transition-duration: 0ms;"></i></div>
      <div class="var-hover-overlay"></div>
    </button><input class="var-counter__input" inputmode="numeric"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--default var-button--round var-counter__increment-button" type="button" var-counter-cover="">
      <!--v-if-->
      <div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus" style="transition-duration: 0ms;"></i></div>
      <div class="var-hover-overlay"></div>
    </button></div>
  <transition-stub name="var-form-details" appear="false" persisted="false" css="true">
    <!--v-if-->
  </transition-stub>
</div>"
`;

exports[`counter elevation 2`] = `
"<div class="var-counter var--box">
  <div class="var-counter__controller"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--default var-button--round var-counter__decrement-button" type="button" var-counter-cover="">
      <!--v-if-->
      <div class="var-button__content"><i class="var-icon var-icon--set var-icon-minus" style="transition-duration: 0ms;"></i></div>
      <div class="var-hover-overlay"></div>
    </button><input class="var-counter__input" inputmode="numeric"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--default var-button--round var-counter__increment-button" type="button" var-counter-cover="">
      <!--v-if-->
      <div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus" style="transition-duration: 0ms;"></i></div>
      <div class="var-hover-overlay"></div>
    </button></div>
  <transition-stub name="var-form-details" appear="false" persisted="false" css="true">
    <!--v-if-->
  </transition-stub>
</div>"
`;

exports[`counter incrementButton 1`] = `
"<div class="var-counter var--box">
  <div class="var-counter__controller var-elevation--2"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--default var-button--round var-counter__decrement-button" type="button" var-counter-cover="">
      <!--v-if-->
      <div class="var-button__content"><i class="var-icon var-icon--set var-icon-minus" style="transition-duration: 0ms;"></i></div>
      <div class="var-hover-overlay"></div>
    </button><input class="var-counter__input" inputmode="numeric"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--default var-button--round var-counter__increment-button" type="button" var-counter-cover="">
      <!--v-if-->
      <div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus" style="transition-duration: 0ms;"></i></div>
      <div class="var-hover-overlay"></div>
    </button></div>
  <transition-stub name="var-form-details" appear="false" persisted="false" css="true">
    <!--v-if-->
  </transition-stub>
</div>"
`;

exports[`counter incrementButton 2`] = `
"<div class="var-counter var--box">
  <div class="var-counter__controller var-elevation--2"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--default var-button--round var-counter__decrement-button" type="button" var-counter-cover="">
      <!--v-if-->
      <div class="var-button__content"><i class="var-icon var-icon--set var-icon-minus" style="transition-duration: 0ms;"></i></div>
      <div class="var-hover-overlay"></div>
    </button><input class="var-counter__input" inputmode="numeric"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--default var-button--round var-counter__increment-button var-counter--hidden" type="button" var-counter-cover="">
      <!--v-if-->
      <div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus" style="transition-duration: 0ms;"></i></div>
      <div class="var-hover-overlay"></div>
    </button></div>
  <transition-stub name="var-form-details" appear="false" persisted="false" css="true">
    <!--v-if-->
  </transition-stub>
</div>"
`;

exports[`counter validation 1`] = `
"<div class="var-counter var--box">
  <div class="var-counter__controller var-elevation--2 var-counter--error"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--default var-button--round var-counter__decrement-button" type="button" var-counter-cover="">
      <!--v-if-->
      <div class="var-button__content"><i class="var-icon var-icon--set var-icon-minus" style="transition-duration: 0ms;"></i></div>
      <div class="var-hover-overlay"></div>
    </button><input class="var-counter__input" inputmode="numeric"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--default var-button--round var-counter__increment-button" type="button" var-counter-cover="">
      <!--v-if-->
      <div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus" style="transition-duration: 0ms;"></i></div>
      <div class="var-hover-overlay"></div>
    </button></div>
  <transition-stub name="var-form-details" appear="false" persisted="false" css="true">
    <div class="var-form-details">
      <div class="var-form-details__error-message">
        <transition-stub name="var-form-details__message" appear="false" persisted="false" css="true">
          <div>The value must be more than zero</div>
        </transition-stub>
      </div>
      <div class="var-form-details__extra-message">
        <transition-stub name="var-form-details__message" appear="false" persisted="false" css="true">
          <!--v-if-->
        </transition-stub>
      </div>
    </div>
  </transition-stub>
</div>"
`;

exports[`counter validation with zod 1`] = `
"<div class="var-counter var--box">
  <div class="var-counter__controller var-elevation--2 var-counter--error"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--default var-button--round var-counter__decrement-button" type="button" var-counter-cover="">
      <!--v-if-->
      <div class="var-button__content"><i class="var-icon var-icon--set var-icon-minus" style="transition-duration: 0ms;"></i></div>
      <div class="var-hover-overlay"></div>
    </button><input class="var-counter__input" inputmode="numeric"><button class="var-button var--box var-button--normal var--inline-flex var-elevation--2 var-button--default var-button--round var-counter__increment-button" type="button" var-counter-cover="">
      <!--v-if-->
      <div class="var-button__content"><i class="var-icon var-icon--set var-icon-plus" style="transition-duration: 0ms;"></i></div>
      <div class="var-hover-overlay"></div>
    </button></div>
  <transition-stub name="var-form-details" appear="false" persisted="false" css="true">
    <div class="var-form-details">
      <div class="var-form-details__error-message">
        <transition-stub name="var-form-details__message" appear="false" persisted="false" css="true">
          <div>The value must be more than zero</div>
        </transition-stub>
      </div>
      <div class="var-form-details__extra-message">
        <transition-stub name="var-form-details__message" appear="false" persisted="false" css="true">
          <!--v-if-->
        </transition-stub>
      </div>
    </div>
  </transition-stub>
</div>"
`;
