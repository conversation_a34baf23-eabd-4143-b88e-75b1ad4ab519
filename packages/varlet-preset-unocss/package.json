{"name": "@varlet/preset-unocss", "version": "3.11.1", "description": "unocss preset of varlet", "keywords": ["unocss", "preset", "varlet"], "bugs": {"url": "https://github.com/varletjs/varlet/issues"}, "repository": {"type": "git", "url": "https://github.com/varletjs/varlet.git"}, "license": "MIT", "author": "haoziqaq <<EMAIL>>", "sideEffects": false, "type": "module", "exports": {".": {"import": "./lib/index.js", "require": "./lib/index.cjs", "types": "./lib/index.d.ts"}, "./*": "./*"}, "main": "lib/index.cjs", "module": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "scripts": {"build": "tsup src/index.ts --format esm,cjs --out-dir=lib --dts --clean", "dev": "tsup src/index.ts --format esm --out-dir=lib --watch --dts"}, "devDependencies": {"@types/node": "catalog:", "tsup": "catalog:", "typescript": "catalog:"}, "peerDependencies": {"unocss": "^0.58.5"}}