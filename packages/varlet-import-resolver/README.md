# Varlet Auto Import Resolver

English | [简体中文](./README.zh-CN.md)

`@varlet/import-resolver` is a resolver for [unplugin-vue-components](https://github.com/unplugin/unplugin-vue-components) that enables on-demand importing of Varlet components.

## Features

- Supports `Vite`, `Webpack`, `Vue CLI`, `Rollup`, `esbuild`, and more.
- Automatically imports the corresponding CSS styles for the components.
- Supports SSR (Server-Side Rendering).

## Automatic Import

via plugin
[unplugin-vue-components](https://github.com/antfu/unplugin-vue-components) and
[unplugin-auto-import](https://github.com/antfu/unplugin-auto-import)
Implement components to automatically import on demand, This is our most recommended way.

### Install Plugin

```shell
# npm
npm i @varlet/import-resolver unplugin-vue-components unplugin-auto-import -D

# yarn
yarn add @varlet/import-resolver unplugin-vue-components unplugin-auto-import -D

# pnpm
pnpm add @varlet/import-resolver unplugin-vue-components unplugin-auto-import -D
```

### Vite

```js
// vite.config.js
import vue from '@vitejs/plugin-vue'
import components from 'unplugin-vue-components/vite'
import autoImport from 'unplugin-auto-import/vite'
import { VarletImportResolver } from '@varlet/import-resolver'
import { defineConfig } from 'vite'

export default defineConfig({
  plugins: [
    vue(),
    components({
      resolvers: [VarletImportResolver()]
    }),
    autoImport({
      resolvers: [VarletImportResolver({ autoImport: true })]
    })
  ]
})
```

### Vue CLI

```js
// vue.config.js
const Components = require('unplugin-vue-components/webpack')
const AutoImport = require('unplugin-auto-import/webpack')
const { VarletImportResolver } = require('@varlet/import-resolver')

module.exports = {
  configureWebpack: {
    plugins: [
      Components.default({
        resolvers: [VarletImportResolver()]
      }),
      AutoImport.default({
        resolvers: [VarletImportResolver({ autoImport: true })]
      })
    ]
  }
}
```

### Typescript configuration note

In order to get good IDE syntax highlighting,
please make sure that the type declaration files generated by the above two plugins are include by `typescript`,
which can be configured as follows in `tsconfig.json`:

```json
{
  "include": ["auto-imports.d.ts", "components.d.ts"]
}
```

### Manual import

Each component is a `Vue plugin` and consists of `component logic` and `style files`, which are manually imported and used as follows.

```js
import App from './App.vue'
import { createApp } from 'vue'
import { Button } from '@varlet/ui'
import '@varlet/ui/es/button/style/index'

createApp(App).use(Button)
```

OR

```html
<script setup>
import { Button as VarButton } from '@varlet/ui'
import '@varlet/ui/es/button/style/index'
</script>

<template>
  <var-button>Say Hello</var-button>
</template>
```

### Manual import and automatic import comparison

#### Comparison-Manual import

```html
<script setup>
import { Button as VarButton, Snackbar } from '@varlet/ui'
import '@varlet/ui/es/button/style/index'
import '@varlet/ui/es/snackbar/style/index'

function handleClick() {
  Snackbar('Hello!')
}
</script>

<template>
  <var-button @click="handleClick">Say Hello</var-button>
</template>
```

#### Comparison-Automatic import

```html
<script setup>
function handleClick() {
  Snackbar('Hello!')
}
</script>

<template>
  <var-button @click="handleClick">Say Hello</var-button>
</template>
```