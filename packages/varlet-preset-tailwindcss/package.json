{"name": "@varlet/preset-tailwindcss", "version": "3.11.1", "description": "tailwindcss preset of varlet", "keywords": ["tailwindcss", "preset", "varlet"], "bugs": {"url": "https://github.com/varletjs/varlet/issues"}, "repository": {"type": "git", "url": "https://github.com/varletjs/varlet.git"}, "license": "MIT", "author": "chouchouji <<EMAIL>>", "sideEffects": false, "type": "module", "exports": {".": {"import": "./lib/index.js", "require": "./lib/index.cjs", "types": "./lib/index.d.ts"}, "./*": "./*"}, "main": "lib/index.cjs", "module": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "scripts": {"build": "tsup src/index.ts --format esm,cjs --out-dir=lib --dts --clean", "dev": "tsup src/index.ts --format esm --out-dir=lib --watch --dts"}, "devDependencies": {"@types/node": "catalog:", "tsup": "catalog:", "typescript": "catalog:"}, "peerDependencies": {"tailwindcss": "^3.4.1"}}