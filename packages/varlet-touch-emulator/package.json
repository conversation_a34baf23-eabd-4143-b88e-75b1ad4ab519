{"name": "@varlet/touch-emulator", "version": "3.11.1", "description": "touch-emulator", "keywords": ["emulator", "varlet"], "homepage": "https://github.com/varletjs/varlet#readme", "bugs": {"url": "https://github.com/varletjs/varlet/issues"}, "repository": {"type": "git", "url": "git+https://github.com/varletjs/varlet.git"}, "license": "MIT", "author": "BeADre <<EMAIL>>", "type": "module", "main": "index.js", "module": "index.js", "files": ["iife.js", "index.js"], "scripts": {"build": "tsup index.js --format iife --out-dir=. --globalName=VarletTouchEmulator && node build.js"}, "devDependencies": {"tsup": "catalog:"}}