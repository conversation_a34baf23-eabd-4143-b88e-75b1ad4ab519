<template>
  <div :class="n()"></div>
</template>

<script lang="ts">
  import { defineComponent } from 'vue'
  import { props } from './props'
  import { createNamespace } from '../utils/components'

  const { name, n, classes } = createNamespace('<%- kebabCaseName %>')

  export default defineComponent({
    name,
    props,
    setup(props) {
      return {
        n,
      }
    },
  })
</script>

<style lang="less">
  @import './<%- camelizeName %>';
</style>
