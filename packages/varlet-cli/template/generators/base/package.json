{"name": "@varlet/ui", "version": "0.0.0", "description": "A components library example", "main": "lib/varlet.cjs.js", "module": "es/index.mjs", "typings": "types/index.d.ts", "web-types": "highlight/web-types.en-US.json", "keywords": ["<PERSON><PERSON>", "UI"], "license": "MIT", "sideEffects": ["es/**/style/*", "lib/**/style/*", "es/style.mjs", "lib/style.js", "*.css"], "scripts": {"preinstall": "npx only-allow pnpm", "postinstall": "simple-git-hooks", "dev": "varlet-cli dev", "build": "varlet-cli build", "preview": "varlet-cli preview", "compile": "varlet-cli compile", "lint": "eslint . --fix", "format": "prettier --write .", "changelog": "varlet-cli changelog", "release": "pnpm compile && varlet-cli release", "test": "varlet-cli test", "test:watch": "varlet-cli test -w", "test:coverage": "varlet-cli test -cov", "create": "varlet-cli create"}, "peerDependencies": {"vue": "^3.2.0"}, "devDependencies": {"@varlet/cli": "workspace:*", "@varlet/icons": "workspace:*", "@varlet/touch-emulator": "workspace:*", "@varlet/shared": "workspace:*", "@varlet/ui": "workspace:*", "@vue/test-utils": "2.4.6", "@vue/runtime-core": "3.5.13", "@vitest/coverage-istanbul": "3.0.6", "@configurajs/eslint": "*", "@configurajs/prettier": "*", "jsdom": "24.1.1", "vitest": "3.0.6", "clipboard": "^2.0.6", "eslint": "^9.17.0", "lint-staged": "^10.5.0", "live-server": "^1.2.1", "prettier": "^3.5.2", "simple-git-hooks": "^2.8.0", "typescript": "5.3.3", "vue": "3.5.13", "vue-router": "4.5.0"}, "lint-staged": {"*.{ts,tsx,js,vue,less}": "prettier --write", "*.{ts,tsx,js,vue}": "eslint --fix"}, "simple-git-hooks": {"pre-commit": "pnpm exec lint-staged --allow-empty --concurrent false", "commit-msg": "pnpm exec varlet-cli commit-lint -p $1"}, "packageManager": "pnpm@9.1.1", "engines": {"pnpm": ">=9.0"}}